import { useState, useRef, useCallback } from 'react';

export interface UseTimerReturn {
  tempo: number;
  rodando: boolean;
  contandoRegressivo: boolean;
  contagemRegressiva: number;
  iniciarTimer: () => void;
  pausarTimer: () => void;
  resetarTimer: () => void;
  pararTimer: () => void;
}

export function useTimer(): UseTimerReturn {
  const [tempo, setTempo] = useState<number>(0);
  const [rodando, setRodando] = useState<boolean>(false);
  const [contandoRegressivo, setContandoRegressivo] = useState<boolean>(false);
  const [contagemRegressiva, setContagemRegressiva] = useState<number>(3);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const regressivaRef = useRef<NodeJS.Timeout | null>(null);

  const limparIntervalos = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (regressivaRef.current) {
      clearInterval(regressivaRef.current);
      regressivaRef.current = null;
    }
  }, []);

  const iniciarTimer = useCallback(() => {
    if (rodando || contandoRegressivo) return;

    setContandoRegressivo(true);
    setContagemRegressiva(3);

    regressivaRef.current = setInterval(() => {
      setContagemRegressiva((prev) => {
        if (prev <= 1) {
          if (regressivaRef.current) {
            clearInterval(regressivaRef.current);
            regressivaRef.current = null;
          }
          setContandoRegressivo(false);
          setRodando(true);

          // Iniciar cronômetro principal
          intervalRef.current = setInterval(() => {
            setTempo((prevTempo) => prevTempo + 1);
          }, 1000);

          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, [rodando, contandoRegressivo]);

  const pausarTimer = useCallback(() => {
    if (!rodando) return;
    
    setRodando(false);
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, [rodando]);

  const resetarTimer = useCallback(() => {
    limparIntervalos();
    setTempo(0);
    setRodando(false);
    setContandoRegressivo(false);
    setContagemRegressiva(3);
  }, [limparIntervalos]);

  const pararTimer = useCallback(() => {
    limparIntervalos();
    setRodando(false);
    setContandoRegressivo(false);
    setContagemRegressiva(3);
  }, [limparIntervalos]);

  return {
    tempo,
    rodando,
    contandoRegressivo,
    contagemRegressiva,
    iniciarTimer,
    pausarTimer,
    resetarTimer,
    pararTimer
  };
}
