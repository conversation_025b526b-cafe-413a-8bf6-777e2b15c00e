'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { initGA, trackPageView } from '@/lib/analytics';
import { initializeConsentMode } from '@/lib/consent';

export function GoogleAnalytics() {
  const pathname = usePathname();

  useEffect(() => {
    // Inicializar Google Consent Mode primeiro
    initializeConsentMode();

    // Depois inicializar GA4
    initGA();
  }, []);

  useEffect(() => {
    // Rastrear mudanças de página
    if (pathname) {
      trackPageView(pathname);
    }
  }, [pathname]);

  return (
    <>
      {/* Google Tag Manager / gtag.js script */}
      <script
        async
        src={`https://www.googletagmanager.com/gtag/js?id=G-QCCMM3EWFJ`}
      />
    </>
  );
}
