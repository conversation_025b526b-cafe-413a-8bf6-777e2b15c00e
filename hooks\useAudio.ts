import { useRef, useCallback } from 'react';

export interface UseAudioReturn {
  audioRef: React.RefObject<HTMLAudioElement>;
  finishAudioRef: React.RefObject<HTMLAudioElement>;
  transitionAudioRef: React.RefObject<HTMLAudioElement>;
  playBeep: () => void;
  playFinish: () => void;
  playTransition: () => void;
}

export function useAudio(): UseAudioReturn {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const finishAudioRef = useRef<HTMLAudioElement | null>(null);
  const transitionAudioRef = useRef<HTMLAudioElement | null>(null);

  const playBeep = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      audioRef.current.play().catch(console.error);
    }
  }, []);

  const playFinish = useCallback(() => {
    if (finishAudioRef.current) {
      finishAudioRef.current.currentTime = 0;
      finishAudioRef.current.play().catch(console.error);
    }
  }, []);

  const playTransition = useCallback(() => {
    if (transitionAudioRef.current) {
      transitionAudioRef.current.currentTime = 0;
      transitionAudioRef.current.play().catch(console.error);
    }
  }, []);

  return {
    audioRef,
    finishAudioRef,
    transitionAudioRef,
    playBeep,
    playFinish,
    playTransition
  };
}
