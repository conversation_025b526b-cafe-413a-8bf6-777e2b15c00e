import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Outfit } from "next/font/google";
import "./globals.css";
import { GoogleAnalytics } from '@/components/GoogleAnalytics';
import { GoogleAdSense } from '@/components/GoogleAdSense';
import { ConsentManager } from '@/components/ConsentManager';

const outfit = Outfit({
  variable: "--font-outfit",
  subsets: ["latin"],
  display: "swap", // Otimização de carregamento de fonte
  preload: true,
});

export const metadata: Metadata = {
  metadataBase: new URL('https://cereja-app.vercel.app'),
  title: {
    default: "Cereja - Calculadora 4:6",
    template: "%s | Cereja"
  },
  description: "Calculadora profissional para o método 4:6 de Tetsu Kasuya. Crie receitas personalizadas de café coado com controle de acidez, doçura e corpo. Cronômetro integrado e interface otimizada.",
  keywords: ["café", "método 4:6", "Tetsu Kasuya", "calculadora café", "receita café", "café coado", "v60", "hario", "brewing", "extração"],
  authors: [{ name: "<PERSON>", url: "https://ko-fi.com/pedrogott" }],
  creator: "Pedro Gottardi",
  publisher: "Cereja",
  category: "Food & Drink",
  classification: "Coffee Brewing Calculator",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Cereja",
    startupImage: "/apple-icon.png",
  },
  formatDetection: {
    telephone: false,
    email: false,
    address: false,
  },
  manifest: "/manifest.json",
  // Open Graph
  openGraph: {
    type: "website",
    locale: "pt_BR",
    url: "https://cereja-app.vercel.app",
    siteName: "Cereja",
    title: "Cereja - Calculadora 4:6",
    description: "Calculadora profissional para o método 4:6 de Tetsu Kasuya. Crie receitas personalizadas de café coado.",
    images: [
      {
        url: "/seo.png",
        width: 1200,
        height: 630,
        alt: "Cereja - Calculadora do Método 4:6",
        type: "image/png",
      }
    ],
  },
  // Twitter Cards
  twitter: {
    card: "summary_large_image",
    site: "@cereja_app",
    creator: "@pedrogott",
    title: "Cereja - Calculadora 4:6",
    description: "Calculadora profissional para o método 4:6 de Tetsu Kasuya",
    images: ["/seo.png"],
  },
  // Robots
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  // Verificação
  verification: {
    google: "google-site-verification-code", // Substituir pelo código real
  },
  // Outros
  alternates: {
    canonical: "https://cereja-app.vercel.app",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#8b5cf6" },
    { media: "(prefers-color-scheme: dark)", color: "#8b5cf6" }
  ],
  colorScheme: "light dark",
};

// Structured Data JSON-LD
const structuredData = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "Cereja",
  "description": "Calculadora profissional para o método 4:6 de Tetsu Kasuya",
  "url": "https://cereja-app.vercel.app",
  "applicationCategory": "UtilityApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "BRL"
  },
  "author": {
    "@type": "Person",
    "name": "Pedro Gottardi",
    "url": "https://ko-fi.com/pedrogott"
  },
  "publisher": {
    "@type": "Organization",
    "name": "Cereja"
  },
  "featureList": [
    "Calculadora do método 4:6",
    "Cronômetro integrado",
    "Controle de perfil de sabor",
    "Controle de perfil de corpo",
    "Receitas personalizadas"
  ],
  "screenshot": "https://cereja-app.vercel.app/seo.png"
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <head>
        {/* Preconnect para melhor performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />
        <link rel="preconnect" href="https://www.google-analytics.com" />

        {/* DNS Prefetch */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />
        <link rel="dns-prefetch" href="//www.googletagmanager.com" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </head>
      <body
        className={`${outfit.variable} antialiased`}
      >
        <GoogleAnalytics />
        <GoogleAdSense />
        {children}
        <ConsentManager />
      </body>
    </html>
  );
}
