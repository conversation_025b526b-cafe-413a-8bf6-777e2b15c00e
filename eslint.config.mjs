import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    plugins: {
      "react-compiler": (await import("eslint-plugin-react-compiler")).default,
    },
    rules: {
      // Regras do React Compiler para detectar violações das Rules of Hooks
      "react-compiler/react-compiler": "error",
    },
  },
];

export default eslintConfig;
