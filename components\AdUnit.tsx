'use client';

import { useEffect } from 'react';

interface AdUnitProps {
  /** Slot ID do anúncio (fornecido pelo Google AdSense) */
  adSlot: string;
  /** Formato do anúncio (ex: 'auto', 'rectangle', 'banner') */
  adFormat?: string;
  /** Layout do anúncio para anúncios responsivos */
  adLayout?: string;
  /** Chave de layout para anúncios responsivos */
  adLayoutKey?: string;
  /** Estilo customizado para o container do anúncio */
  style?: React.CSSProperties;
  /** Classe CSS para o container do anúncio */
  className?: string;
}

/**
 * Componente para exibir anúncios do Google AdSense
 *
 * Este componente deve ser usado apenas quando necessário posicionamento manual
 * de anúncios. Para a maioria dos casos, os Auto Ads são suficientes.
 *
 * @example
 * ```tsx
 * <AdUnit
 *   adSlot="1234567890"
 *   adFormat="auto"
 *   className="my-4"
 * />
 * ```
 */
export function AdUnit({
  adSlot,
  adFormat = 'auto',
  adLayout,
  adLayoutKey,
  style,
  className = ''
}: AdUnitProps) {
  useEffect(() => {
    // Verificar se o AdSense está disponível
    if (typeof window !== 'undefined') {
      const windowWithAds = window as Window & {
        adsbygoogle?: unknown[];
      };

      // Verificar se AdSense está carregado e não foi bloqueado
      if (windowWithAds.adsbygoogle) {
        try {
          // Inicializar o anúncio
          (windowWithAds.adsbygoogle = windowWithAds.adsbygoogle || []).push({});
        } catch {
          // Falha silenciosa - não mostrar erro se bloqueador estiver ativo
          if (process.env.NODE_ENV === 'development') {
            console.log('AdSense não disponível - possivelmente bloqueado');
          }
        }
      } else {
        // AdSense não está disponível (provavelmente bloqueado)
        if (process.env.NODE_ENV === 'development') {
          console.log('AdSense não carregado - anúncio não será exibido');
        }
      }
    }
  }, []);

  return (
    <div className={`adsense-container ${className}`} style={style}>
      <ins
        className="adsbygoogle"
        style={{ display: 'block' }}
        data-ad-client="ca-pub-2733184476160559"
        data-ad-slot={adSlot}
        data-ad-format={adFormat}
        data-ad-layout={adLayout}
        data-ad-layout-key={adLayoutKey}
        data-full-width-responsive="true"
      />
    </div>
  );
}

/**
 * Componente para anúncio responsivo padrão
 * Configuração otimizada para a maioria dos casos de uso
 */
export function ResponsiveAd({
  adSlot,
  className = '',
  style
}: {
  adSlot: string;
  className?: string;
  style?: React.CSSProperties;
}) {
  return (
    <AdUnit
      adSlot={adSlot}
      adFormat="auto"
      className={`responsive-ad ${className}`}
      style={style}
    />
  );
}

/**
 * Componente para anúncio banner horizontal
 * Ideal para posicionamento no topo ou rodapé
 */
export function BannerAd({
  adSlot,
  className = '',
  style
}: {
  adSlot: string;
  className?: string;
  style?: React.CSSProperties;
}) {
  return (
    <AdUnit
      adSlot={adSlot}
      adFormat="auto"
      className={`banner-ad ${className}`}
      style={{
        width: '100%',
        height: 'auto',
        ...style
      }}
    />
  );
}

/**
 * Componente para anúncio quadrado/retângulo
 * Ideal para sidebar ou entre conteúdo
 */
export function SquareAd({
  adSlot,
  className = '',
  style
}: {
  adSlot: string;
  className?: string;
  style?: React.CSSProperties;
}) {
  return (
    <AdUnit
      adSlot={adSlot}
      adFormat="rectangle"
      className={`square-ad ${className}`}
      style={style}
    />
  );
}
