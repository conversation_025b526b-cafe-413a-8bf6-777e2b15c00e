import React from 'react';
import Image from 'next/image';

interface ProfileOption {
  nome: string;
  icone: string;
  alt: string;
}

interface ProfileSelectorProps {
  title: string;
  selectedProfile: string;
  options: ProfileOption[];
  onProfileChange: (profile: string) => void;
  inputsDesabilitados: boolean;
}

export function ProfileSelector({
  title,
  selectedProfile,
  options,
  onProfileChange,
  inputsDesabilitados
}: ProfileSelectorProps) {
  return (
    <div className="mb-4 sm:mb-6">
      <h3 className="text-sm sm:text-base font-medium text-foreground mb-2 sm:mb-3">
        {title}
      </h3>
      <div className="grid grid-cols-3 gap-2 sm:gap-3">
        {options.map((opcao) => (
          <button
            key={opcao.nome}
            onClick={() => !inputsDesabilitados && onProfileChange(opcao.nome)}
            disabled={inputsDesabilitados}
            className={`px-3 sm:px-8 py-3 sm:py-4 rounded-md text-xs sm:text-sm font-medium transition-colors flex flex-col items-center gap-2 min-h-touch touch-target ${
              inputsDesabilitados
                ? "cursor-not-allowed opacity-50"
                : "cursor-pointer"
            } ${
              selectedProfile === opcao.nome
                ? "bg-primary text-primary-foreground shadow-sm"
                : "bg-card text-card-foreground border border-border hover:bg-accent hover:text-accent-foreground"
            }`}
          >
            <Image
              src={opcao.icone}
              alt={opcao.alt}
              width={24}
              height={24}
              className="object-contain sm:w-8 sm:h-8"
              quality={75}
            />
            <span className="text-center leading-tight">{opcao.nome}</span>
          </button>
        ))}
      </div>
    </div>
  );
}
