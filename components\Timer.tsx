import React from 'react';
import { Play, Pause, RotateCcw } from 'lucide-react';

interface TimerProps {
  tempo: number;
  rodando: boolean;
  contandoRegressivo: boolean;
  contagemRegressiva: number;
  onIniciar: () => void;
  onPausar: () => void;
  onResetar: () => void;
}

export function Timer({
  tempo,
  rodando,
  contandoRegressivo,
  contagemRegressiva,
  onIniciar,
  onPausar,
  onResetar
}: TimerProps) {
  const formatarTempo = (segundos: number): string => {
    const minutos = Math.floor(segundos / 60);
    const segs = segundos % 60;
    return `${minutos}:${segs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-card border border-border rounded-lg p-4 sm:p-6 mb-4 sm:mb-6">
      <div className="text-center">
        <div className="text-4xl sm:text-6xl font-mono font-bold text-foreground mb-4 sm:mb-6">
          {contandoRegressivo ? (
            <span className="text-primary animate-pulse">
              {contagemRegressiva > 0 ? contagemRegressiva : "GO!"}
            </span>
          ) : (
            formatarTempo(tempo)
          )}
        </div>
        
        <div className="flex justify-center gap-2 sm:gap-4">
          {!rodando && !contandoRegressivo ? (
            <button
              onClick={onIniciar}
              className="bg-primary text-primary-foreground px-4 sm:px-6 py-2 sm:py-3 rounded-md font-medium transition-colors hover:bg-primary/90 active:bg-primary/80 flex items-center gap-2 min-h-touch touch-target"
            >
              <Play size={20} className="sm:w-5 sm:h-5" strokeWidth={1.5} />
              <span className="text-sm sm:text-base">Iniciar</span>
            </button>
          ) : rodando ? (
            <button
              onClick={onPausar}
              className="bg-secondary text-secondary-foreground px-4 sm:px-6 py-2 sm:py-3 rounded-md font-medium transition-colors hover:bg-secondary/90 active:bg-secondary/80 flex items-center gap-2 min-h-touch touch-target"
            >
              <Pause size={20} className="sm:w-5 sm:h-5" strokeWidth={1.5} />
              <span className="text-sm sm:text-base">Pausar</span>
            </button>
          ) : null}
          
          {(tempo > 0 || rodando || contandoRegressivo) && (
            <button
              onClick={onResetar}
              className="bg-destructive text-destructive-foreground px-4 sm:px-6 py-2 sm:py-3 rounded-md font-medium transition-colors hover:bg-destructive/90 active:bg-destructive/80 flex items-center gap-2 min-h-touch touch-target"
            >
              <RotateCcw size={20} className="sm:w-5 sm:h-5" strokeWidth={1.5} />
              <span className="text-sm sm:text-base">Resetar</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
