import { useState, useCallback } from 'react';

export interface RecipeStep {
  passo: number;
  tempo: string;
  acao: string;
  agua: string;
  observacao?: string;
}

export interface UseRecipeReturn {
  cafe: string;
  agua: string;
  proporcao: string;
  perfilSabor: string;
  perfilCorpo: string;
  aguaBloqueada: boolean;
  proporcaoBloqueada: boolean;
  receita: RecipeStep[];
  setCafe: (valor: string) => void;
  setAgua: (valor: string) => void;
  setProporcao: (valor: string) => void;
  setPerfilSabor: (perfil: string) => void;
  setPerfilCorpo: (perfil: string) => void;
  setAguaBloqueada: (bloqueada: boolean) => void;
  setProporcaoBloqueada: (bloqueada: boolean) => void;
  handleCafeChange: (valor: string, inputsDesabilitados: boolean) => void;
  handleAguaChange: (valor: string, inputsDesabilitados: boolean) => void;
  handleProporcaoChange: (valor: string, inputsDesabilitados: boolean) => void;
  calcularReceita: () => void;
  arredondar: (valor: number) => string;
}

export function useRecipe(): UseRecipeReturn {
  const [cafe, setCafe] = useState<string>("15");
  const [agua, setAgua] = useState<string>("225");
  const [proporcao, setProporcao] = useState<string>("15");
  const [perfilSabor, setPerfilSabor] = useState<string>("Equilibrado");
  const [perfilCorpo, setPerfilCorpo] = useState<string>("Equilibrado");
  const [aguaBloqueada, setAguaBloqueada] = useState<boolean>(true);
  const [proporcaoBloqueada, setProporcaoBloqueada] = useState<boolean>(true);
  const [receita, setReceita] = useState<RecipeStep[]>([]);

  const arredondar = useCallback((valor: number): string => {
    return Math.round(valor) + "";
  }, []);

  const handleCafeChange = useCallback((valor: string, inputsDesabilitados: boolean) => {
    if (inputsDesabilitados) return;
    setCafe(valor);
    const numCafe = parseFloat(valor) || 0;
    const numProporcao = parseFloat(proporcao) || 0;
    if (numCafe > 0 && numProporcao > 0) {
      setAgua(arredondar(numCafe * numProporcao));
    }
  }, [proporcao, arredondar]);

  const handleAguaChange = useCallback((valor: string, inputsDesabilitados: boolean) => {
    if (aguaBloqueada || inputsDesabilitados) return;
    setAgua(valor);
    const numAgua = parseFloat(valor) || 0;
    const numProporcao = parseFloat(proporcao) || 0;
    if (numAgua > 0 && numProporcao > 0) {
      setCafe(arredondar(numAgua / numProporcao));
    }
  }, [aguaBloqueada, proporcao, arredondar]);

  const handleProporcaoChange = useCallback((valor: string, inputsDesabilitados: boolean) => {
    if (proporcaoBloqueada || inputsDesabilitados) return;
    setProporcao(valor);
    const numCafe = parseFloat(cafe) || 0;
    const numProporcao = parseFloat(valor) || 0;
    if (numCafe > 0 && numProporcao > 0) {
      setAgua(arredondar(numCafe * numProporcao));
    }
  }, [proporcaoBloqueada, cafe, arredondar]);

  const calcularReceita = useCallback(() => {
    const numCafe = parseFloat(cafe) || 0;
    const numAgua = parseFloat(agua) || 0;

    if (numCafe <= 0 || numAgua <= 0) {
      setReceita([]);
      return;
    }

    // Lógica do método 4:6
    const aguaTotal = numAgua;
    const primeira40 = aguaTotal * 0.4;
    const segunda60 = aguaTotal * 0.6;

    let novaReceita: RecipeStep[] = [];

    // Primeira fase (40% da água) - controla acidez/doçura
    if (perfilSabor === "Mais Acidez") {
      const primeira = primeira40 * 0.6;
      const segunda = primeira40 * 0.4;
      novaReceita.push(
        { passo: 1, tempo: "0:00", acao: "Bloom", agua: arredondar(primeira), observacao: "Molhe todo o pó" },
        { passo: 2, tempo: "0:45", acao: "Despejo", agua: arredondar(segunda) }
      );
    } else if (perfilSabor === "Mais Doçura") {
      const primeira = primeira40 * 0.5;
      const segunda = primeira40 * 0.3;
      const terceira = primeira40 * 0.2;
      novaReceita.push(
        { passo: 1, tempo: "0:00", acao: "Bloom", agua: arredondar(primeira), observacao: "Molhe todo o pó" },
        { passo: 2, tempo: "0:45", acao: "Despejo", agua: arredondar(segunda) },
        { passo: 3, tempo: "1:30", acao: "Despejo", agua: arredondar(terceira) }
      );
    } else {
      const primeira = primeira40 * 0.5;
      const segunda = primeira40 * 0.5;
      novaReceita.push(
        { passo: 1, tempo: "0:00", acao: "Bloom", agua: arredondar(primeira), observacao: "Molhe todo o pó" },
        { passo: 2, tempo: "0:45", acao: "Despejo", agua: arredondar(segunda) }
      );
    }

    // Segunda fase (60% da água) - controla corpo
    const ultimoPasso = novaReceita.length;
    if (perfilCorpo === "Mais Corpo") {
      const quarta = segunda60;
      novaReceita.push(
        { passo: ultimoPasso + 1, tempo: "2:15", acao: "Despejo Final", agua: arredondar(quarta), observacao: "Despejo único para mais corpo" }
      );
    } else {
      const quarta = segunda60 * 0.5;
      const quinta = segunda60 * 0.5;
      novaReceita.push(
        { passo: ultimoPasso + 1, tempo: "2:15", acao: "Despejo", agua: arredondar(quarta) },
        { passo: ultimoPasso + 2, tempo: "3:00", acao: "Despejo Final", agua: arredondar(quinta) }
      );
    }

    setReceita(novaReceita);
  }, [cafe, agua, perfilSabor, perfilCorpo, arredondar]);

  return {
    cafe,
    agua,
    proporcao,
    perfilSabor,
    perfilCorpo,
    aguaBloqueada,
    proporcaoBloqueada,
    receita,
    setCafe,
    setAgua,
    setProporcao,
    setPerfilSabor,
    setPerfilCorpo,
    setAguaBloqueada,
    setProporcaoBloqueada,
    handleCafeChange,
    handleAguaChange,
    handleProporcaoChange,
    calcularReceita,
    arredondar
  };
}
