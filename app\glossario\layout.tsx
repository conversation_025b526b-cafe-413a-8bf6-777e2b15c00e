import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Glossário do Café",
  description: "Glossário completo de termos do café com mais de 100 definições sobre métodos de preparo, variedades, processamento e equipamentos. Aprenda sobre café especial.",
  keywords: ["glossário café", "termos café", "café especial", "métodos preparo", "variedades café", "processamento café", "dicionário café", "terminologia café"],
  openGraph: {
    title: "Glossário do Café | Cereja",
    description: "Glossário completo de termos do café com mais de 100 definições sobre métodos de preparo, variedades, processamento e equipamentos.",
    url: "https://cereja-app.vercel.app/glossario",
    images: [
      {
        url: "/seo.png",
        width: 1200,
        height: 630,
        alt: "Glossário do Café - Cereja",
      }
    ],
  },
  twitter: {
    title: "Glossário do Café | Cereja",
    description: "Glossário completo de termos do café com mais de 100 definições sobre métodos de preparo, variedades, processamento e equipamentos.",
    images: ["/seo.png"],
  },
  alternates: {
    canonical: "https://cereja-app.vercel.app/glossario",
  },
};

// Structured Data específico para o glossário
const glossaryStructuredData = {
  "@context": "https://schema.org",
  "@type": "DefinedTermSet",
  "name": "Glossário do Café",
  "description": "Glossário completo de termos relacionados ao café, métodos de preparo e equipamentos",
  "url": "https://cereja-app.vercel.app/glossario",
  "inDefinedTermSet": "https://cereja-app.vercel.app/glossario",
  "hasDefinedTerm": [
    {
      "@type": "DefinedTerm",
      "name": "Método 4:6",
      "description": "Técnica de preparo de café desenvolvida por Tetsu Kasuya"
    },
    {
      "@type": "DefinedTerm", 
      "name": "Pour Over",
      "description": "Método de preparo manual onde a água quente é despejada lentamente sobre o café moído"
    }
  ]
};

export default function GlossarioLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(glossaryStructuredData) }}
      />
      {children}
    </>
  );
}
