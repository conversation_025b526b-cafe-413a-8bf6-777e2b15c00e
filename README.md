This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## ⚡ React Compiler

Este projeto utiliza o **React Compiler** (React 19+) para otimização automática de componentes:

- ✅ **Ativado**: Compilação automática de componentes React
- ✅ **Configuração conservadora**: Máxima compatibilidade com código existente
- ✅ **ESLint integrado**: Detecção de violações das Rules of Hooks
- ✅ **Zero breaking changes**: Funciona com código existente sem modificações

### Benefícios
- Memoização automática de componentes e hooks
- Redução de re-renders desnecessários
- Melhor performance sem `useMemo`/`useCallback` manuais
- Otimizações em tempo de build

## 📊 Google Analytics 4

Este projeto inclui implementação completa do **Google Analytics 4** para monitoramento de uso:

- ✅ **Tracking de eventos**: Interações com calculadora e glossário
- ✅ **Eventos customizados**: Receitas geradas, termos visualizados, compartilhamentos
- ✅ **Performance otimizada**: Não interfere na experiência do usuário
- ✅ **Privacidade**: Apenas dados agregados e anônimos

### Eventos Principais
- `recipe_generated`: Nova receita criada
- `recipe_completed`: Receita finalizada com sucesso
- `glossary_term_viewed`: Termo do glossário visualizado
- `button_click`: Cliques em botões importantes
- `share_recipe`: Compartilhamento de receita

📖 **Documentação completa**: [docs/GOOGLE_ANALYTICS.md](docs/GOOGLE_ANALYTICS.md)

## 💰 Google AdSense

Monetização implementada com **Google AdSense** usando Auto Ads para otimização automática:

- ✅ **Auto Ads**: Posicionamento inteligente automático
- ✅ **Performance otimizada**: Carregamento assíncrono sem impacto na UX
- ✅ **Responsivo**: Adapta-se a todos os dispositivos
- ✅ **Compatibilidade**: Funciona junto com Google Analytics 4
- ✅ **Ad-Block Friendly**: Detecção silenciosa, sem erros para usuários com bloqueadores

### Características Técnicas
- **Cliente ID**: `ca-pub-2733184476160559`
- **Strategy**: `afterInteractive` para performance otimizada
- **Componentes**: Auto Ads + componentes manuais opcionais
- **TypeScript**: Tipagem completa e segura

📖 **Documentação completa**: [docs/GOOGLE_ADSENSE.md](docs/GOOGLE_ADSENSE.md)

## ⚡ Otimizações de Performance

Implementações focadas em **Core Web Vitals** e experiência do usuário:

- ✅ **Imagens otimizadas**: WebP/AVIF, quality estratégica, priority inteligente
- ✅ **Carregamento eficiente**: Lazy loading, preload apenas para conteúdo crítico
- ✅ **Formatos modernos**: Redução de 25-40% no tamanho das imagens
- ✅ **Zero warnings**: Console limpo, sem alertas de performance

### Métricas Alvo
- **LCP**: < 2.5s (Good)
- **FID**: < 100ms (Good)
- **CLS**: < 0.1 (Good)

📖 **Documentação completa**: [docs/PERFORMANCE_OPTIMIZATIONS.md](docs/PERFORMANCE_OPTIMIZATIONS.md)

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

### Como reverter (se necessário)

Se houver problemas com o React Compiler, você pode desativá-lo facilmente:

1. **Desativar temporariamente**:
   ```js
   // next.config.ts
   const nextConfig: NextConfig = {
     experimental: {
       reactCompiler: false, // Desativa o React Compiler
     },
   };
   ```

2. **Remover completamente**:
   ```bash
   npm uninstall babel-plugin-react-compiler eslint-plugin-react-compiler
   ```

### Configurações aplicadas

- **next.config.ts**: `reactCompiler: true`
- **eslint.config.mjs**: Regras do React Compiler
- **package.json**: Dependências do compilador

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
