'use client';

import { useEffect, useState } from 'react';

// ID do cliente Google AdSense
const ADSENSE_CLIENT_ID = 'ca-pub-2733184476160559';

export function GoogleAdSense() {
  const [, setAdBlockDetected] = useState(false);

  useEffect(() => {
    // Verificar se o script já foi carregado
    if (document.querySelector(`script[src*="adsbygoogle.js"]`)) {
      return;
    }

    // Função para detectar bloqueador de anúncios de forma silenciosa
    const detectAdBlocker = () => {
      return new Promise<boolean>((resolve) => {
        // Criar elemento de teste que bloqueadores costumam bloquear
        const testAd = document.createElement('div');
        testAd.innerHTML = '&nbsp;';
        testAd.className = 'adsbox';
        testAd.style.position = 'absolute';
        testAd.style.left = '-10000px';
        testAd.style.width = '1px';
        testAd.style.height = '1px';

        document.body.appendChild(testAd);

        // Verificar se o elemento foi bloqueado
        setTimeout(() => {
          const isBlocked = testAd.offsetHeight === 0;
          document.body.removeChild(testAd);
          resolve(isBlocked);
        }, 100);
      });
    };

    // Tentar carregar o AdSense apenas se não houver bloqueador
    const loadAdSense = async () => {
      try {
        const hasAdBlocker = await detectAdBlocker();

        if (hasAdBlocker) {
          setAdBlockDetected(true);
          if (process.env.NODE_ENV === 'development') {
            console.log('Bloqueador de anúncios detectado - AdSense não será carregado');
          }
          return;
        }

        // Criar e configurar o script do AdSense
        const script = document.createElement('script');
        script.src = `https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${ADSENSE_CLIENT_ID}`;
        script.async = true;
        script.crossOrigin = 'anonymous';

        // Adicionar listeners silenciosos
        script.onload = () => {
          if (process.env.NODE_ENV === 'development') {
            console.log('Google AdSense carregado com sucesso');
          }
        };

        script.onerror = () => {
          // Falha silenciosa - não mostrar erro para o usuário
          setAdBlockDetected(true);
          if (process.env.NODE_ENV === 'development') {
            console.log('AdSense bloqueado ou falhou ao carregar - continuando sem anúncios');
          }
        };

        // Adicionar o script ao head
        document.head.appendChild(script);

        // Timeout de segurança - se não carregar em 5 segundos, assumir bloqueio
        setTimeout(() => {
          const windowWithAds = window as Window & {
            adsbygoogle?: unknown[];
          };

          if (!windowWithAds.adsbygoogle) {
            setAdBlockDetected(true);
            if (process.env.NODE_ENV === 'development') {
              console.log('AdSense timeout - assumindo bloqueador ativo');
            }
          }
        }, 5000);

      } catch {
        // Falha silenciosa
        setAdBlockDetected(true);
        if (process.env.NODE_ENV === 'development') {
          console.log('Erro na detecção de bloqueador - continuando sem anúncios');
        }
      }
    };

    loadAdSense();

    // Cleanup: remover script quando componente for desmontado
    return () => {
      const existingScript = document.querySelector(`script[src*="adsbygoogle.js"]`);
      if (existingScript) {
        existingScript.remove();
      }
    };
  }, []);

  // Não renderizar nada - componente totalmente silencioso
  return null;
}
