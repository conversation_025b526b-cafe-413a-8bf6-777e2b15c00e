import React from 'react';
import { RecipeStep } from '@/hooks/useRecipe';

interface RecipeStepsProps {
  receita: RecipeStep[];
  tempo: number;
  passoAnterior: number;
  onShare: () => void;
}

export function RecipeSteps({ receita, tempo, passoAnterior, onShare }: RecipeStepsProps) {
  const obterPassoAtual = (): number => {
    if (receita.length === 0) return -1;
    
    for (let i = 0; i < receita.length; i++) {
      const [min, seg] = receita[i].tempo.split(':').map(Number);
      const tempoPassoSegundos = min * 60 + seg;
      
      if (tempo < tempoPassoSegundos) {
        return i - 1;
      }
    }
    
    return receita.length - 1;
  };

  const passoAtual = obterPassoAtual();

  if (receita.length === 0) {
    return (
      <div className="bg-card border border-border rounded-lg p-4 sm:p-6 text-center">
        <p className="text-muted-foreground text-sm sm:text-base">
          Configure os parâmetros e clique em "Gerar Receita" para ver os passos.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-card border border-border rounded-lg p-4 sm:p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg sm:text-xl font-semibold text-foreground">
          Receita Método 4:6
        </h3>
        <button
          onClick={onShare}
          className="bg-primary text-primary-foreground px-3 sm:px-4 py-1.5 sm:py-2 rounded-md text-xs sm:text-sm font-medium transition-colors hover:bg-primary/90 active:bg-primary/80 min-h-touch touch-target"
        >
          Compartilhar
        </button>
      </div>
      
      <div className="space-y-3 sm:space-y-4">
        {receita.map((passo, index) => {
          const isAtual = index === passoAtual;
          const isConcluido = index < passoAtual;
          const isProximo = index === passoAtual + 1;
          
          return (
            <div
              key={passo.passo}
              className={`p-3 sm:p-4 rounded-md border transition-all duration-300 ${
                isAtual
                  ? "bg-primary/10 border-primary shadow-sm"
                  : isConcluido
                  ? "bg-muted/50 border-muted opacity-75"
                  : isProximo
                  ? "bg-accent/50 border-accent"
                  : "bg-background border-border"
              }`}
            >
              <div className="flex items-start justify-between gap-3">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className={`text-xs sm:text-sm font-medium px-2 py-1 rounded ${
                      isAtual
                        ? "bg-primary text-primary-foreground"
                        : isConcluido
                        ? "bg-muted text-muted-foreground"
                        : "bg-secondary text-secondary-foreground"
                    }`}>
                      Passo {passo.passo}
                    </span>
                    <span className="text-xs sm:text-sm text-muted-foreground font-mono">
                      {passo.tempo}
                    </span>
                  </div>
                  <h4 className="font-medium text-foreground text-sm sm:text-base mb-1">
                    {passo.acao}
                  </h4>
                  {passo.observacao && (
                    <p className="text-xs sm:text-sm text-muted-foreground mb-2">
                      {passo.observacao}
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <div className="text-lg sm:text-xl font-bold text-foreground">
                    {passo.agua}ml
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      
      <div className="mt-4 sm:mt-6 pt-4 border-t border-border">
        <div className="flex justify-between text-sm sm:text-base">
          <span className="text-muted-foreground">Total de água:</span>
          <span className="font-semibold text-foreground">
            {receita.reduce((total, passo) => total + parseInt(passo.agua), 0)}ml
          </span>
        </div>
      </div>
    </div>
  );
}
