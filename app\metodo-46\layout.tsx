import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Método 4:6 - Em Construção",
  description: "Página sobre o método 4:6 de Tetsu Kasuya em desenvolvimento. Em breve você encontrará informações detalhadas sobre esta técnica revolucionária de preparo de café.",
  keywords: ["método 4:6", "Tetsu Kasuya", "técnica café", "preparo café", "em construção"],
  robots: {
    index: false, // Não indexar páginas em construção
    follow: true,
  },
  openGraph: {
    title: "Método 4:6 - Em Construção | Cereja",
    description: "Página sobre o método 4:6 de Tetsu Kasuya em desenvolvimento.",
    url: "https://cereja-app.vercel.app/metodo-46",
  },
  alternates: {
    canonical: "https://cereja-app.vercel.app/metodo-46",
  },
};

export default function Metodo46Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
