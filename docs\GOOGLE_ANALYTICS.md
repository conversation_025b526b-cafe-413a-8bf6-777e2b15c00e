# Google Analytics 4 - Implementação Cereja

## 📊 Visão Geral

O Google Analytics 4 foi implementado na aplicação Cereja com ID `G-QCCMM3EWFJ` para rastrear interações dos usuários e fornecer insights sobre o uso da aplicação.

## 🎯 Eventos Implementados

### 1. **Eventos da Calculadora 4:6**

#### `recipe_generated`
- **Quando**: Usuário inicia uma nova receita pela primeira vez
- **Parâmetros**:
  - `cafe_amount`: Quantidade de café em gramas
  - `water_amount`: Quantidade de água em ml
  - `ratio`: Proporção café:água
  - `flavor_profile`: Perfil de sabor selecionado
  - `body_profile`: Perfil de corpo selecionado
  - `recipe_type`: Sempre "v60_4_6_method"

#### `recipe_completed`
- **Quando**: Receita é finalizada (timer atinge o tempo total)
- **Parâmetros**:
  - `completion_time_seconds`: Tempo total da receita
  - `total_steps`: Número de passos da receita
  - `success`: Sempre true

#### `recipe_timer`
- **Quando**: Interações com o timer da receita
- **Parâmetros**:
  - `timer_action`: "start", "pause", "resume", "reset"
  - `current_time_seconds`: Tempo atual (quando aplicável)

#### `share_recipe`
- **Quando**: Usuário compartilha uma receita
- **Parâmetros**:
  - `share_method`: "copy" (cópia para clipboard)

### 2. **Eventos do Glossário**

#### `glossary_term_viewed`
- **Quando**: Usuário clica em um termo do glossário
- **Parâmetros**:
  - `term_name`: Nome do termo visualizado
  - `term_id`: ID único do termo

#### `glossary_filter`
- **Quando**: Usuário usa filtros de busca ou tags
- **Parâmetros**:
  - `filter_type`: "search" ou "tag"
  - `filter_value`: Valor do filtro aplicado

#### `glossary_navigation`
- **Quando**: Navegação dentro do glossário
- **Parâmetros**:
  - `action`: Tipo de navegação ("term_click", "scroll_to_top")
  - Parâmetros adicionais conforme a ação

### 3. **Eventos de Interação (button_click)**

Rastreia cliques em botões importantes:
- **Calculadora**: `iniciar_receita`, `pausar_receita`, `retomar_receita`, `reiniciar_receita`, `abrir_compartilhar`, `copiar_receita`
- **Glossário**: `selecionar_tag`, `desselecionar_tag`, `limpar_busca`, `limpar_tag`, `limpar_tudo`, `voltar_ao_topo`

**Parâmetros**:
- `button_name`: Nome identificador do botão
- `location`: Localização do botão na aplicação

## 🔧 Arquitetura Técnica

### Arquivos Principais

1. **`lib/analytics.ts`**: Utilitário central com todas as funções de tracking
2. **`components/GoogleAnalytics.tsx`**: Componente para inicialização e tracking de páginas
3. **`app/layout.tsx`**: Integração do GA4 no layout principal

### Configuração

- **Modo de Desenvolvimento**: `testMode: true` (não envia dados reais)
- **Modo de Produção**: Tracking completo ativo
- **Inicialização**: Automática no carregamento da aplicação
- **Page Views**: Rastreamento automático de mudanças de rota

## 📈 Como Monitorar

### No Google Analytics 4

1. **Eventos em Tempo Real**:
   - Analytics → Tempo real → Eventos
   - Visualize eventos conforme acontecem

2. **Relatórios de Eventos**:
   - Analytics → Eventos → Todos os eventos
   - Filtre por nome do evento para análises específicas

3. **Conversões Personalizadas**:
   - Configure `recipe_completed` como conversão
   - Monitore taxa de conclusão de receitas

### Eventos Recomendados para Monitorar

- **Engajamento**: `recipe_generated`, `glossary_term_viewed`
- **Conversão**: `recipe_completed`, `share_recipe`
- **Usabilidade**: `button_click` por localização
- **Busca**: `glossary_filter` com `filter_type: "search"`

## 🚀 Extensibilidade

### Adicionando Novos Eventos

```typescript
import { trackEvent } from '@/lib/analytics';

// Evento customizado
trackEvent('custom_event_name', {
  parameter1: 'value1',
  parameter2: 123,
  parameter3: true
});
```

### Funções Disponíveis

- `trackButtonClick(buttonName, location)`
- `trackRecipeGenerated(recipeData)`
- `trackRecipeCompleted(time, steps)`
- `trackRecipeShared(method)`
- `trackGlossaryTermViewed(termName, termId)`
- `trackGlossaryFilter(type, value)`
- `trackGlossaryNavigation(action, details)`
- `trackRecipeTimer(action, currentTime)`

## 🔒 Privacidade

- Nenhum dado pessoal identificável é coletado
- Apenas interações com a aplicação são rastreadas
- Conformidade com LGPD/GDPR
- Dados agregados e anônimos

## 🐛 Debugging

### Verificar se está Funcionando

1. **Console do Navegador**:
   ```javascript
   // Verificar se GA4 está carregado
   console.log(window.gtag);
   ```

2. **Extensão GA Debugger**: Use a extensão do Chrome "Google Analytics Debugger"

3. **Modo de Desenvolvimento**: Eventos são logados no console com `testMode: true`

### Problemas Comuns

- **Eventos não aparecem**: Verifique se está em modo de produção
- **Parâmetros incorretos**: Confirme tipos de dados (string, number, boolean)
- **Inicialização**: Certifique-se que `initGA()` é chamado apenas uma vez

## 📊 Métricas Sugeridas

### KPIs Principais
- Taxa de conclusão de receitas (`recipe_completed` / `recipe_generated`)
- Engajamento com glossário (visualizações de termos)
- Uso de filtros de busca
- Compartilhamentos de receita

### Segmentação
- Por perfil de sabor/corpo selecionado
- Por quantidade de café utilizada
- Por tempo de sessão
- Por dispositivo (mobile/desktop)
