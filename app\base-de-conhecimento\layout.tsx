import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Base de Conhecimento - Em Construção",
  description: "Base de conhecimento sobre café em desenvolvimento. Em breve você encontrará artigos, guias e informações detalhadas sobre o mundo do café especial.",
  keywords: ["base conhecimento", "café especial", "artigos café", "guias café", "em construção"],
  robots: {
    index: false, // Não indexar páginas em construção
    follow: true,
  },
  openGraph: {
    title: "Base de Conhecimento - Em Construção | Cereja",
    description: "Base de conhecimento sobre café em desenvolvimento.",
    url: "https://cereja-app.vercel.app/base-de-conhecimento",
  },
  alternates: {
    canonical: "https://cereja-app.vercel.app/base-de-conhecimento",
  },
};

export default function BaseConhecimentoLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
