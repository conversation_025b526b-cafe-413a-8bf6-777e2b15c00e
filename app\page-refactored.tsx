"use client";

import Image from "next/image";
import Link from "next/link";
import { useState, useEffect, useRef, useCallback } from "react";
import { Menu, X, Home as HomeIcon, BookOpen, Coffee, Settings, Database } from "lucide-react";
import {
  trackButtonClick,
  trackRecipeGenerated,
  trackRecipeCompleted,
  trackRecipeShared,
  trackRecipeTimer
} from "@/lib/analytics";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { useTimer } from "@/hooks/useTimer";
import { useRecipe } from "@/hooks/useRecipe";
import { useAudio } from "@/hooks/useAudio";
import { useScreenReader } from "@/hooks/useA11y";
import { useToast } from "@/components/Toast";
import { RecipeInputs } from "@/components/RecipeInputs";
import { ProfileSelector } from "@/components/ProfileSelector";
import { Timer } from "@/components/Timer";
import { RecipeSteps } from "@/components/RecipeSteps";
import { LoadingButton } from "@/components/LoadingButton";
import { validateRecipe } from "@/lib/validation";

// Structured Data para a página principal
const homeStructuredData = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "Cereja - Calculadora 4:6",
  "description": "Calculadora profissional para o método 4:6 de Tetsu Kasuya",
  "url": "https://cereja-app.vercel.app",
  "applicationCategory": "UtilityApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "BRL"
  },
  "featureList": [
    "Calculadora do método 4:6",
    "Cronômetro integrado com áudio",
    "Controle de perfil de sabor (acidez/doçura)",
    "Controle de perfil de corpo",
    "Receitas personalizadas",
    "Compartilhamento de receitas",
    "Interface responsiva"
  ],
  "screenshot": "https://cereja-app.vercel.app/seo.png",
  "author": {
    "@type": "Person",
    "name": "Pedro Gottardi",
    "url": "https://ko-fi.com/pedrogott"
  },
  "about": {
    "@type": "Thing",
    "name": "Método 4:6",
    "description": "Técnica de preparo de café desenvolvida por Tetsu Kasuya"
  }
};

export default function Home() {
  // Estados da interface
  const [modalAberto, setModalAberto] = useState<boolean>(false);
  const [sidebarAberta, setSidebarAberta] = useState<boolean>(false);
  const [sidebarFechando, setSidebarFechando] = useState<boolean>(false);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  
  // Refs
  const passoAnteriorRef = useRef<number>(-1);
  const receitaRef = useRef<HTMLDivElement | null>(null);
  const sidebarRef = useRef<HTMLDivElement | null>(null);

  // Hooks customizados
  const timer = useTimer();
  const recipe = useRecipe();
  const audio = useAudio();
  const { announce } = useScreenReader();
  const { addToast } = useToast();

  // Persistência local
  const [preferences, setPreferences] = useLocalStorage('cereja-preferencias', {
    cafe: "15",
    proporcao: "15",
    perfilSabor: "Equilibrado",
    perfilCorpo: "Equilibrado",
    aguaBloqueada: true,
    proporcaoBloqueada: true
  });

  // Carregar preferências salvas
  useEffect(() => {
    if (preferences) {
      recipe.setCafe(preferences.cafe);
      recipe.setProporcao(preferences.proporcao);
      recipe.setPerfilSabor(preferences.perfilSabor);
      recipe.setPerfilCorpo(preferences.perfilCorpo);
      recipe.setAguaBloqueada(preferences.aguaBloqueada);
      recipe.setProporcaoBloqueada(preferences.proporcaoBloqueada);
      
      // Recalcular água
      const numCafe = parseFloat(preferences.cafe);
      const numProporcao = parseFloat(preferences.proporcao);
      if (numCafe > 0 && numProporcao > 0) {
        recipe.setAgua(recipe.arredondar(numCafe * numProporcao));
      }
    }
  }, []);

  // Salvar preferências quando mudarem
  useEffect(() => {
    const newPreferences = {
      cafe: recipe.cafe,
      proporcao: recipe.proporcao,
      perfilSabor: recipe.perfilSabor,
      perfilCorpo: recipe.perfilCorpo,
      aguaBloqueada: recipe.aguaBloqueada,
      proporcaoBloqueada: recipe.proporcaoBloqueada
    };
    setPreferences(newPreferences);
  }, [recipe.cafe, recipe.proporcao, recipe.perfilSabor, recipe.perfilCorpo, recipe.aguaBloqueada, recipe.proporcaoBloqueada, setPreferences]);

  // Função para verificar se os inputs devem estar desabilitados
  const inputsDesabilitados = useCallback(() => {
    return timer.rodando || timer.contandoRegressivo || timer.tempo > 0;
  }, [timer.rodando, timer.contandoRegressivo, timer.tempo]);

  // Handlers para inputs
  const handleCafeChange = useCallback((valor: string) => {
    recipe.handleCafeChange(valor, inputsDesabilitados());
  }, [recipe, inputsDesabilitados]);

  const handleAguaChange = useCallback((valor: string) => {
    recipe.handleAguaChange(valor, inputsDesabilitados());
  }, [recipe, inputsDesabilitados]);

  const handleProporcaoChange = useCallback((valor: string) => {
    recipe.handleProporcaoChange(valor, inputsDesabilitados());
  }, [recipe, inputsDesabilitados]);

  // Função para gerar receita com validação
  const handleGerarReceita = useCallback(async () => {
    setIsGenerating(true);
    
    try {
      // Validar dados
      const validation = validateRecipe(
        recipe.cafe,
        recipe.agua,
        recipe.proporcao,
        recipe.perfilSabor,
        recipe.perfilCorpo
      );

      if (!validation.isValid) {
        addToast({
          type: 'error',
          title: 'Dados inválidos',
          description: validation.errors[0]
        });
        return;
      }

      // Gerar receita
      recipe.calcularReceita();
      
      // Analytics
      trackRecipeGenerated();
      
      // Anunciar para screen readers
      announce('Receita gerada com sucesso');
      
      // Toast de sucesso
      addToast({
        type: 'success',
        title: 'Receita gerada!',
        description: 'Sua receita personalizada está pronta.'
      });

      // Scroll para a receita
      setTimeout(() => {
        receitaRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);

    } catch (error) {
      console.error('Erro ao gerar receita:', error);
      addToast({
        type: 'error',
        title: 'Erro',
        description: 'Ocorreu um erro ao gerar a receita. Tente novamente.'
      });
    } finally {
      setIsGenerating(false);
    }
  }, [recipe, addToast, announce]);

  // Função para compartilhar receita
  const handleCompartilhar = useCallback(async () => {
    try {
      const texto = `Receita Cereja - Método 4:6\n\nCafé: ${recipe.cafe}g\nÁgua: ${recipe.agua}ml\nProporção: 1:${recipe.proporcao}\nPerfil de Sabor: ${recipe.perfilSabor}\nPerfil de Corpo: ${recipe.perfilCorpo}\n\nAcesse: https://cereja-app.vercel.app`;
      
      if (navigator.share) {
        await navigator.share({
          title: 'Receita Cereja - Método 4:6',
          text: texto,
          url: 'https://cereja-app.vercel.app'
        });
      } else {
        await navigator.clipboard.writeText(texto);
        addToast({
          type: 'success',
          title: 'Receita copiada!',
          description: 'A receita foi copiada para a área de transferência.'
        });
      }
      
      trackRecipeShared();
    } catch (error) {
      console.error('Erro ao compartilhar:', error);
      addToast({
        type: 'error',
        title: 'Erro ao compartilhar',
        description: 'Não foi possível compartilhar a receita.'
      });
    }
  }, [recipe, addToast]);

  // Função para selecionar todo o texto ao clicar
  const handleFocus = useCallback((e: React.FocusEvent<HTMLInputElement>) => {
    e.target.select();
  }, []);

  // Função para desabilitar scroll e setas nos inputs
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "ArrowUp" || e.key === "ArrowDown") {
      e.preventDefault();
    }
  }, []);

  const handleWheel = useCallback((e: React.WheelEvent<HTMLInputElement>) => {
    e.currentTarget.blur();
  }, []);

  // Opções de perfil
  const perfilSaborOptions = [
    { nome: "Mais Acidez", icone: "/images/acidez.png", alt: "Ícone Mais Acidez - Perfil de sabor ácido para café" },
    { nome: "Equilibrado", icone: "/images/equilibrado.png", alt: "Ícone Equilibrado - Perfil de sabor equilibrado para café" },
    { nome: "Mais Doçura", icone: "/images/docura.png", alt: "Ícone Mais Doçura - Perfil de sabor doce para café" }
  ];

  const perfilCorpoOptions = [
    { nome: "Menos Corpo", icone: "/images/menos-corpo.png", alt: "Ícone Menos Corpo - Perfil de corpo leve para café" },
    { nome: "Equilibrado", icone: "/images/equilibrado.png", alt: "Ícone Equilibrado - Perfil de corpo equilibrado para café" },
    { nome: "Mais Corpo", icone: "/images/mais-corpo.png", alt: "Ícone Mais Corpo - Perfil de corpo encorpado para café" }
  ];

  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col">
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(homeStructuredData) }}
      />

      {/* Header com menu */}
      <header className="flex justify-between items-center p-4 sm:p-6 border-b border-border">
        <div className="flex items-center gap-2">
          <Image
            src="/cereja.png"
            alt="Logo Cereja"
            width={32}
            height={32}
            className="object-contain"
            quality={90}
          />
          <span className="font-semibold text-lg">Cereja</span>
        </div>
        
        <button
          onClick={() => setSidebarAberta(true)}
          className="p-2 hover:bg-accent rounded-md transition-colors"
          aria-label="Abrir menu"
        >
          <Menu size={24} />
        </button>
      </header>

      {/* Conteúdo principal */}
      <main className="flex-1 container mx-auto px-4 py-8 max-w-4xl">
        {/* Título */}
        <div className="text-center mb-8">
          <h1 className="text-4xl sm:text-6xl font-bold mb-2">cereja</h1>
          <p className="text-muted-foreground">calculadora do método 4:6</p>
        </div>

        {/* Inputs da receita */}
        <RecipeInputs
          cafe={recipe.cafe}
          agua={recipe.agua}
          proporcao={recipe.proporcao}
          aguaBloqueada={recipe.aguaBloqueada}
          proporcaoBloqueada={recipe.proporcaoBloqueada}
          inputsDesabilitados={inputsDesabilitados()}
          onCafeChange={handleCafeChange}
          onAguaChange={handleAguaChange}
          onProporcaoChange={handleProporcaoChange}
          onAguaBloqueadaChange={recipe.setAguaBloqueada}
          onProporcaoBloqueadaChange={recipe.setProporcaoBloqueada}
          onFocus={handleFocus}
          onKeyDown={handleKeyDown}
          onWheel={handleWheel}
        />

        {/* Seletores de perfil */}
        <ProfileSelector
          title="Perfil de Sabor"
          selectedProfile={recipe.perfilSabor}
          options={perfilSaborOptions}
          onProfileChange={recipe.setPerfilSabor}
          inputsDesabilitados={inputsDesabilitados()}
        />

        <ProfileSelector
          title="Perfil de Corpo"
          selectedProfile={recipe.perfilCorpo}
          options={perfilCorpoOptions}
          onProfileChange={recipe.setPerfilCorpo}
          inputsDesabilitados={inputsDesabilitados()}
        />

        {/* Botão gerar receita */}
        <div className="text-center mb-8">
          <LoadingButton
            onClick={handleGerarReceita}
            loading={isGenerating}
            disabled={inputsDesabilitados()}
            size="lg"
          >
            Gerar Receita
          </LoadingButton>
        </div>

        {/* Timer */}
        <Timer
          tempo={timer.tempo}
          rodando={timer.rodando}
          contandoRegressivo={timer.contandoRegressivo}
          contagemRegressiva={timer.contagemRegressiva}
          onIniciar={timer.iniciarTimer}
          onPausar={timer.pausarTimer}
          onResetar={timer.resetarTimer}
        />

        {/* Receita */}
        <div ref={receitaRef}>
          <RecipeSteps
            receita={recipe.receita}
            tempo={timer.tempo}
            passoAnterior={passoAnteriorRef.current}
            onShare={handleCompartilhar}
          />
        </div>
      </main>

      {/* Áudios */}
      <audio ref={audio.audioRef} preload="auto">
        <source src="/beep.mp3" type="audio/mpeg" />
      </audio>
      <audio ref={audio.finishAudioRef} preload="auto">
        <source src="/finish.mp3" type="audio/mpeg" />
      </audio>
      <audio ref={audio.transitionAudioRef} preload="auto">
        <source src="/transition.mp3" type="audio/mpeg" />
      </audio>
    </div>
  );
}
