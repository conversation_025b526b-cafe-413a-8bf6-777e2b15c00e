import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Política de Privacidade e Cookies",
  description: "Política de privacidade e uso de cookies da Cereja. Saiba como protegemos seus dados e como utilizamos cookies para melhorar sua experiência.",
  keywords: ["política privacidade", "cookies", "LGPD", "GDPR", "proteção dados", "privacidade"],
  robots: {
    index: true,
    follow: false, // Não seguir links desta página
  },
  openGraph: {
    title: "Política de Privacidade | Cereja",
    description: "Política de privacidade e uso de cookies da Cereja. Saiba como protegemos seus dados.",
    url: "https://cereja-app.vercel.app/privacidade",
  },
  twitter: {
    title: "Política de Privacidade | Cereja",
    description: "Política de privacidade e uso de cookies da Cereja. Saiba como protegemos seus dados.",
  },
  alternates: {
    canonical: "https://cereja-app.vercel.app/privacidade",
  },
};

export default function PrivacidadeLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
