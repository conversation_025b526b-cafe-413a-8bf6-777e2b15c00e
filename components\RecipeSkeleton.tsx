import React from 'react';

export function RecipeSkeleton() {
  return (
    <div className="bg-card border border-border rounded-lg p-4 sm:p-6 animate-pulse">
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <div className="h-6 bg-muted rounded w-48"></div>
        <div className="h-8 bg-muted rounded w-24"></div>
      </div>
      
      {/* Steps */}
      <div className="space-y-3 sm:space-y-4">
        {[1, 2, 3, 4].map((index) => (
          <div key={index} className="p-3 sm:p-4 rounded-md border border-border bg-background">
            <div className="flex items-start justify-between gap-3">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-6 bg-muted rounded w-16"></div>
                  <div className="h-4 bg-muted rounded w-12"></div>
                </div>
                <div className="h-5 bg-muted rounded w-24 mb-2"></div>
                <div className="h-4 bg-muted rounded w-32"></div>
              </div>
              <div className="text-right">
                <div className="h-6 bg-muted rounded w-16"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Footer */}
      <div className="mt-4 sm:mt-6 pt-4 border-t border-border">
        <div className="flex justify-between">
          <div className="h-4 bg-muted rounded w-24"></div>
          <div className="h-4 bg-muted rounded w-16"></div>
        </div>
      </div>
    </div>
  );
}

export function InputSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="h-4 bg-muted rounded w-16 mb-2"></div>
      <div className="h-10 bg-muted rounded w-full"></div>
    </div>
  );
}

export function ProfileSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="h-5 bg-muted rounded w-32 mb-3"></div>
      <div className="grid grid-cols-3 gap-2 sm:gap-3">
        {[1, 2, 3].map((index) => (
          <div key={index} className="h-20 bg-muted rounded"></div>
        ))}
      </div>
    </div>
  );
}

export function TimerSkeleton() {
  return (
    <div className="bg-card border border-border rounded-lg p-4 sm:p-6 animate-pulse">
      <div className="text-center">
        <div className="h-16 bg-muted rounded w-32 mx-auto mb-6"></div>
        <div className="flex justify-center gap-4">
          <div className="h-10 bg-muted rounded w-24"></div>
          <div className="h-10 bg-muted rounded w-24"></div>
        </div>
      </div>
    </div>
  );
}
