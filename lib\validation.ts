// Esquemas de validação para a aplicação Cereja

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  value?: number;
}

export interface RecipeValidation {
  cafe: ValidationResult;
  agua: ValidationResult;
  proporcao: ValidationResult;
  isValid: boolean;
  errors: string[];
}

// Validação para quantidade de café
export function validateCafe(value: string): ValidationResult {
  const trimmed = value.trim();
  
  if (!trimmed) {
    return { isValid: false, error: "Quantidade de café é obrigatória" };
  }
  
  const num = parseFloat(trimmed);
  
  if (isNaN(num)) {
    return { isValid: false, error: "Quantidade de café deve ser um número válido" };
  }
  
  if (num <= 0) {
    return { isValid: false, error: "Quantidade de café deve ser maior que zero" };
  }
  
  if (num > 100) {
    return { isValid: false, error: "Quantidade de café não pode ser maior que 100g" };
  }
  
  if (num < 5) {
    return { isValid: false, error: "Quantidade mínima de café é 5g" };
  }
  
  return { isValid: true, value: num };
}

// Validação para quantidade de água
export function validateAgua(value: string): ValidationResult {
  const trimmed = value.trim();
  
  if (!trimmed) {
    return { isValid: false, error: "Quantidade de água é obrigatória" };
  }
  
  const num = parseFloat(trimmed);
  
  if (isNaN(num)) {
    return { isValid: false, error: "Quantidade de água deve ser um número válido" };
  }
  
  if (num <= 0) {
    return { isValid: false, error: "Quantidade de água deve ser maior que zero" };
  }
  
  if (num > 2000) {
    return { isValid: false, error: "Quantidade de água não pode ser maior que 2000ml" };
  }
  
  if (num < 50) {
    return { isValid: false, error: "Quantidade mínima de água é 50ml" };
  }
  
  return { isValid: true, value: num };
}

// Validação para proporção
export function validateProporcao(value: string): ValidationResult {
  const trimmed = value.trim();
  
  if (!trimmed) {
    return { isValid: false, error: "Proporção é obrigatória" };
  }
  
  const num = parseFloat(trimmed);
  
  if (isNaN(num)) {
    return { isValid: false, error: "Proporção deve ser um número válido" };
  }
  
  if (num <= 0) {
    return { isValid: false, error: "Proporção deve ser maior que zero" };
  }
  
  if (num < 10) {
    return { isValid: false, error: "Proporção mínima é 1:10" };
  }
  
  if (num > 20) {
    return { isValid: false, error: "Proporção máxima é 1:20" };
  }
  
  return { isValid: true, value: num };
}

// Validação para perfil de sabor
export function validatePerfilSabor(value: string): ValidationResult {
  const validProfiles = ["Mais Acidez", "Equilibrado", "Mais Doçura"];
  
  if (!validProfiles.includes(value)) {
    return { isValid: false, error: "Perfil de sabor inválido" };
  }
  
  return { isValid: true };
}

// Validação para perfil de corpo
export function validatePerfilCorpo(value: string): ValidationResult {
  const validProfiles = ["Menos Corpo", "Equilibrado", "Mais Corpo"];
  
  if (!validProfiles.includes(value)) {
    return { isValid: false, error: "Perfil de corpo inválido" };
  }
  
  return { isValid: true };
}

// Validação completa da receita
export function validateRecipe(
  cafe: string,
  agua: string,
  proporcao: string,
  perfilSabor: string,
  perfilCorpo: string
): RecipeValidation {
  const cafeValidation = validateCafe(cafe);
  const aguaValidation = validateAgua(agua);
  const proporcaoValidation = validateProporcao(proporcao);
  const saborValidation = validatePerfilSabor(perfilSabor);
  const corpoValidation = validatePerfilCorpo(perfilCorpo);
  
  const errors: string[] = [];
  
  if (!cafeValidation.isValid && cafeValidation.error) {
    errors.push(cafeValidation.error);
  }
  
  if (!aguaValidation.isValid && aguaValidation.error) {
    errors.push(aguaValidation.error);
  }
  
  if (!proporcaoValidation.isValid && proporcaoValidation.error) {
    errors.push(proporcaoValidation.error);
  }
  
  if (!saborValidation.isValid && saborValidation.error) {
    errors.push(saborValidation.error);
  }
  
  if (!corpoValidation.isValid && corpoValidation.error) {
    errors.push(corpoValidation.error);
  }
  
  // Validação de proporção entre café e água
  if (cafeValidation.isValid && aguaValidation.isValid && cafeValidation.value && aguaValidation.value) {
    const ratio = aguaValidation.value / cafeValidation.value;
    if (ratio < 10 || ratio > 20) {
      errors.push(`Proporção café:água (1:${ratio.toFixed(1)}) está fora do recomendado (1:10 a 1:20)`);
    }
  }
  
  return {
    cafe: cafeValidation,
    agua: aguaValidation,
    proporcao: proporcaoValidation,
    isValid: errors.length === 0,
    errors
  };
}

// Sanitização de input numérico
export function sanitizeNumericInput(value: string): string {
  // Remove caracteres não numéricos exceto ponto e vírgula
  let sanitized = value.replace(/[^0-9.,]/g, '');
  
  // Substitui vírgula por ponto
  sanitized = sanitized.replace(',', '.');
  
  // Remove pontos extras (mantém apenas o primeiro)
  const parts = sanitized.split('.');
  if (parts.length > 2) {
    sanitized = parts[0] + '.' + parts.slice(1).join('');
  }
  
  return sanitized;
}

// Formatação de número para exibição
export function formatNumber(value: number, decimals: number = 1): string {
  return value.toFixed(decimals).replace(/\.?0+$/, '');
}
