import React from 'react';
import { Lock, Unlock } from 'lucide-react';

interface RecipeInputsProps {
  cafe: string;
  agua: string;
  proporcao: string;
  aguaBloqueada: boolean;
  proporcaoBloqueada: boolean;
  inputsDesabilitados: boolean;
  onCafeChange: (valor: string) => void;
  onAguaChange: (valor: string) => void;
  onProporcaoChange: (valor: string) => void;
  onAguaBloqueadaChange: (bloqueada: boolean) => void;
  onProporcaoBloqueadaChange: (bloqueada: boolean) => void;
  onFocus: (e: React.FocusEvent<HTMLInputElement>) => void;
  onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onWheel: (e: React.WheelEvent<HTMLInputElement>) => void;
}

export function RecipeInputs({
  cafe,
  agua,
  proporcao,
  aguaBloqueada,
  proporcaoBloqueada,
  inputsDesabilitados,
  onCafeChange,
  onAguaChange,
  onProporcaoChange,
  onAguaBloqueadaChange,
  onProporcaoBloqueadaChange,
  onFocus,
  onKeyDown,
  onWheel
}: RecipeInputsProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4 mb-4 sm:mb-6">
      {/* Input Café */}
      <div>
        <label htmlFor="cafe-input" className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
          Café (g)
        </label>
        <input
          id="cafe-input"
          name="cafe"
          type="number"
          autoComplete="off"
          value={cafe}
          onChange={(e) => onCafeChange(e.target.value)}
          onFocus={onFocus}
          onKeyDown={onKeyDown}
          onWheel={onWheel}
          min="0"
          step="0.1"
          disabled={inputsDesabilitados}
          className={`w-full px-3 py-2.5 sm:py-2 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
            inputsDesabilitados
              ? "bg-muted text-muted-foreground cursor-not-allowed"
              : "bg-background"
          }`}
        />
      </div>

      {/* Input Água */}
      <div>
        <label htmlFor="agua-input" className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
          Água (ml)
        </label>
        <div className="relative">
          <input
            id="agua-input"
            name="agua"
            type="number"
            autoComplete="off"
            value={agua}
            onChange={(e) => onAguaChange(e.target.value)}
            onFocus={onFocus}
            onKeyDown={onKeyDown}
            onWheel={onWheel}
            min="0"
            step="0.1"
            disabled={aguaBloqueada || inputsDesabilitados}
            className={`w-full px-3 py-2.5 sm:py-2 pr-12 sm:pr-10 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
              aguaBloqueada || inputsDesabilitados
                ? "bg-muted text-muted-foreground cursor-not-allowed"
                : "bg-background"
            }`}
          />
          <button
            type="button"
            onClick={() => !inputsDesabilitados && onAguaBloqueadaChange(!aguaBloqueada)}
            disabled={inputsDesabilitados}
            className={`absolute right-1 sm:right-2 top-1/2 transform -translate-y-1/2 p-2 sm:p-1 transition-colors min-w-[44px] min-h-[44px] sm:min-w-auto sm:min-h-auto flex items-center justify-center touch-target ${
              inputsDesabilitados
                ? "text-muted-foreground cursor-not-allowed"
                : "text-muted-foreground hover:text-foreground active:text-primary"
            }`}
          >
            {aguaBloqueada ? <Lock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} /> : <Unlock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} />}
          </button>
        </div>
      </div>

      {/* Input Proporção */}
      <div>
        <label htmlFor="proporcao-input" className="block text-xs sm:text-sm font-medium text-foreground mb-1 sm:mb-2">
          Proporção (1:{proporcao})
        </label>
        <div className="relative">
          <input
            id="proporcao-input"
            name="proporcao"
            type="number"
            autoComplete="off"
            value={proporcao}
            onChange={(e) => onProporcaoChange(e.target.value)}
            onFocus={onFocus}
            onKeyDown={onKeyDown}
            onWheel={onWheel}
            min="0"
            step="0.1"
            disabled={proporcaoBloqueada || inputsDesabilitados}
            className={`w-full px-3 py-2.5 sm:py-2 pr-12 sm:pr-10 border border-border rounded-md text-foreground focus:outline-none focus:ring-2 focus:ring-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none text-base ${
              proporcaoBloqueada || inputsDesabilitados
                ? "bg-muted text-muted-foreground cursor-not-allowed"
                : "bg-background"
            }`}
          />
          <button
            type="button"
            onClick={() => !inputsDesabilitados && onProporcaoBloqueadaChange(!proporcaoBloqueada)}
            disabled={inputsDesabilitados}
            className={`absolute right-1 sm:right-2 top-1/2 transform -translate-y-1/2 p-2 sm:p-1 transition-colors min-w-[44px] min-h-[44px] sm:min-w-auto sm:min-h-auto flex items-center justify-center touch-target ${
              inputsDesabilitados
                ? "text-muted-foreground cursor-not-allowed"
                : "text-muted-foreground hover:text-foreground active:text-primary"
            }`}
          >
            {proporcaoBloqueada ? <Lock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} /> : <Unlock size={20} className="sm:w-4 sm:h-4" strokeWidth={1.5} />}
          </button>
        </div>
      </div>
    </div>
  );
}
