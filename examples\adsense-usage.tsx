/**
 * Exemplos de uso dos componentes Google AdSense
 * 
 * IMPORTANTE: A aplicação já está configurada com Auto Ads, que é a abordagem recomendada.
 * Use estes componentes apenas se precisar de controle específico sobre posicionamento.
 */

import { ResponsiveAd, BannerAd, SquareAd, AdUnit } from '@/components/AdUnit';

// Exemplo 1: Anúncio responsivo básico
export function ExemploResponsivo() {
  return (
    <div className="container mx-auto px-4">
      <h1>Minha Página</h1>
      
      {/* Anúncio responsivo entre conteúdo */}
      <ResponsiveAd 
        adSlot="1234567890" // Substitua pelo seu slot ID real
        className="my-8"
      />
      
      <p>Conteúdo da página...</p>
    </div>
  );
}

// Exemplo 2: Banner no topo da página
export function ExemploBannerTopo() {
  return (
    <div>
      {/* Banner no topo */}
      <BannerAd 
        adSlot="1234567890"
        className="mb-6"
        style={{ maxHeight: '120px' }}
      />
      
      <main className="container mx-auto px-4">
        <h1>Conteúdo Principal</h1>
        <p>Texto da página...</p>
      </main>
    </div>
  );
}

// Exemplo 3: Anúncio na sidebar
export function ExemploSidebar() {
  return (
    <div className="flex gap-6">
      <main className="flex-1">
        <h1>Conteúdo Principal</h1>
        <p>Artigo ou conteúdo...</p>
      </main>
      
      <aside className="w-80">
        <h2>Sidebar</h2>
        
        {/* Anúncio quadrado na sidebar */}
        <SquareAd 
          adSlot="1234567890"
          className="mb-4"
        />
        
        <div>Outros widgets da sidebar...</div>
      </aside>
    </div>
  );
}

// Exemplo 4: Múltiplos anúncios em uma página
export function ExemploMultiplosAnuncios() {
  return (
    <div className="container mx-auto px-4">
      {/* Banner no topo */}
      <BannerAd 
        adSlot="1111111111"
        className="mb-8"
      />
      
      <article>
        <h1>Título do Artigo</h1>
        
        <p>Primeiro parágrafo...</p>
        <p>Segundo parágrafo...</p>
        
        {/* Anúncio no meio do conteúdo */}
        <ResponsiveAd 
          adSlot="2222222222"
          className="my-8"
        />
        
        <p>Continuação do artigo...</p>
        <p>Mais conteúdo...</p>
        
        {/* Anúncio no final */}
        <SquareAd 
          adSlot="3333333333"
          className="mt-8 mx-auto"
          style={{ maxWidth: '336px' }}
        />
      </article>
    </div>
  );
}

// Exemplo 5: Anúncio customizado com configurações avançadas
export function ExemploCustomizado() {
  return (
    <div className="container mx-auto px-4">
      <h1>Página com Anúncio Customizado</h1>
      
      {/* Anúncio com configurações específicas */}
      <AdUnit
        adSlot="4444444444"
        adFormat="fluid"
        adLayout="in-article"
        className="my-8 p-4 border border-gray-200 rounded-lg"
        style={{ 
          minHeight: '200px',
          backgroundColor: '#f9f9f9'
        }}
      />
      
      <p>Conteúdo após o anúncio...</p>
    </div>
  );
}

// Exemplo 6: Anúncio condicional (baseado em props ou estado)
export function ExemploCondicional({ mostrarAnuncio = true, isPremium = false }) {
  return (
    <div className="container mx-auto px-4">
      <h1>Conteúdo</h1>
      
      <p>Primeiro parágrafo...</p>
      
      {/* Mostrar anúncio apenas se não for usuário premium */}
      {mostrarAnuncio && !isPremium && (
        <ResponsiveAd 
          adSlot="5555555555"
          className="my-6"
        />
      )}
      
      <p>Resto do conteúdo...</p>
    </div>
  );
}

// Exemplo 7: Anúncio responsivo com diferentes tamanhos
export function ExemploResponsivoAvancado() {
  return (
    <div className="container mx-auto px-4">
      <h1>Anúncio Responsivo Avançado</h1>
      
      {/* Anúncio que se adapta ao container */}
      <div className="w-full max-w-4xl mx-auto">
        <AdUnit
          adSlot="6666666666"
          adFormat="auto"
          className="my-8"
          style={{
            display: 'block',
            width: '100%',
            height: 'auto'
          }}
        />
      </div>
      
      <p>Conteúdo...</p>
    </div>
  );
}

/**
 * NOTAS IMPORTANTES:
 * 
 * 1. SLOT IDS: Substitua os IDs de exemplo (1234567890, etc.) pelos seus slot IDs reais
 *    obtidos no painel do Google AdSense.
 * 
 * 2. AUTO ADS: A aplicação já usa Auto Ads, que é mais eficiente na maioria dos casos.
 *    Use anúncios manuais apenas quando precisar de controle específico.
 * 
 * 3. PERFORMANCE: Evite muitos anúncios manuais na mesma página para não impactar
 *    a experiência do usuário.
 * 
 * 4. TESTE: Sempre teste em diferentes dispositivos e tamanhos de tela.
 * 
 * 5. POLÍTICAS: Certifique-se de seguir as políticas do Google AdSense sobre
 *    posicionamento e quantidade de anúncios.
 */
