import ReactGA from 'react-ga4';
import { isCategoryAccepted } from './consent';

// ID do Google Analytics 4
const GA_MEASUREMENT_ID = 'G-QCCMM3EWFJ';

// Declaração global para gtag
declare global {
  interface Window {
    gtag: (...args: unknown[]) => void;
    dataLayer: unknown[];
  }
}

// Inicializar o Google Analytics
export const initGA = () => {
  if (typeof window !== 'undefined') {
    // Configurar gtag se ainda não foi configurado
    if (!window.gtag) {
      window.dataLayer = window.dataLayer || [];
      window.gtag = function(...args: unknown[]) {
        window.dataLayer.push(args);
      };
    }

    // Configurar Google Analytics com o ID
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_title: document.title,
      page_location: window.location.href,
    });

    // Também inicializar ReactGA para compatibilidade
    ReactGA.initialize(GA_MEASUREMENT_ID, {
      testMode: process.env.NODE_ENV === 'development',
    });
  }
};

// Rastrear visualização de página
export const trackPageView = (path: string, title?: string) => {
  if (typeof window !== 'undefined' && isCategoryAccepted('analytics')) {
    // Usar gtag diretamente para melhor integração com consent mode
    window.gtag('event', 'page_view', {
      page_title: title || document.title,
      page_location: window.location.href,
      page_path: path,
    });

    // Também usar ReactGA para compatibilidade
    ReactGA.send({
      hitType: 'pageview',
      page: path,
      title: title || document.title,
    });
  }
};

// Eventos personalizados
export const trackEvent = (action: string, parameters?: Record<string, string | number | boolean>) => {
  if (typeof window !== 'undefined' && isCategoryAccepted('analytics')) {
    // Usar gtag diretamente
    window.gtag('event', action, parameters);

    // Também usar ReactGA para compatibilidade
    ReactGA.event(action, parameters);
  }
};

// Eventos específicos da aplicação

// Clique em botão
export const trackButtonClick = (buttonName: string, location?: string) => {
  trackEvent('button_click', {
    button_name: buttonName,
    location: location || 'unknown',
  });
};

// Receita gerada/iniciada
export const trackRecipeGenerated = (recipeData: {
  cafe: number;
  agua: number;
  proporcao: number;
  perfilSabor: string;
  perfilCorpo: string;
}) => {
  trackEvent('recipe_generated', {
    cafe_amount: recipeData.cafe,
    water_amount: recipeData.agua,
    ratio: recipeData.proporcao,
    flavor_profile: recipeData.perfilSabor,
    body_profile: recipeData.perfilCorpo,
    recipe_type: 'v60_4_6_method',
  });
};

// Receita finalizada
export const trackRecipeCompleted = (completionTime: number, totalSteps: number) => {
  trackEvent('recipe_completed', {
    completion_time_seconds: completionTime,
    total_steps: totalSteps,
    success: true,
  });
};

// Termo do glossário visualizado
export const trackGlossaryTermViewed = (termName: string, termId: string) => {
  trackEvent('glossary_term_viewed', {
    term_name: termName,
    term_id: termId,
  });
};

// Compartilhamento de receita
export const trackRecipeShared = (shareMethod: 'copy' | 'native_share') => {
  trackEvent('share_recipe', {
    share_method: shareMethod,
  });
};

// Navegação no glossário
export const trackGlossaryNavigation = (action: string, details?: Record<string, string | number | boolean>) => {
  trackEvent('glossary_navigation', {
    action,
    ...details,
  });
};

// Interação com filtros do glossário
export const trackGlossaryFilter = (filterType: 'search' | 'tag', filterValue: string) => {
  trackEvent('glossary_filter', {
    filter_type: filterType,
    filter_value: filterValue,
  });
};

// Timer da receita
export const trackRecipeTimer = (action: 'start' | 'pause' | 'resume' | 'reset', currentTime?: number) => {
  const eventData: Record<string, string | number | boolean> = {
    timer_action: action,
  };

  if (currentTime !== undefined) {
    eventData.current_time_seconds = currentTime;
  }

  trackEvent('recipe_timer', eventData);
};
