'use client';

import { useState } from 'react';
import { CookieBanner } from './CookieBanner';
import { CookieSettingsModal } from './CookieSettingsModal';

export function ConsentManager() {
  const [showSettings, setShowSettings] = useState(false);

  const handleOpenSettings = () => {
    setShowSettings(true);
  };

  const handleCloseSettings = () => {
    setShowSettings(false);
  };

  return (
    <>
      {/* Banner de cookies - aparece apenas se o usuário não fez escolhas */}
      <CookieBanner onOpenSettings={handleOpenSettings} />

      {/* Modal de configurações */}
      <CookieSettingsModal
        isOpen={showSettings}
        onClose={handleCloseSettings}
      />
    </>
  );
}
