# Otimizações de Performance - Cereja

## 📊 Visão Geral

Este documento detalha as otimizações de performance implementadas na aplicação Cereja para garantir carregamento rápido e experiência fluida do usuário.

## 🖼️ Otimizações de Imagem

### Problema Identificado
```
The resource http://localhost:3000/_next/image?url=%2Fcereja.png&w=48&q=100 was preloaded using link preload but not used within a few seconds from the window's load event.
```

### Soluções Implementadas

#### 1. **Uso Estratégico do `priority`**
- ✅ **Mantido**: Apenas na imagem principal do logo (above the fold)
- ❌ **Removido**: Imagens da sidebar (não visível inicialmente)
- ❌ **Removido**: Ícones de perfil (não críticos para LCP)

```typescript
// ✅ CORRETO - Imagem principal (crítica)
<Image
  src="/cereja.png"
  alt="Cereja"
  width={80}
  height={80}
  priority // Mantido - above the fold
  quality={90}
/>

// ✅ CORRETO - Imagem da sidebar (não crítica)
<Image
  src="/cereja.png"
  alt="Cereja"
  width={40}
  height={40}
  // priority removido - não é above the fold
  quality={75}
/>
```

#### 2. **Otimização de Qualidade**
- **Imagem principal**: `quality={90}` (alta qualidade, visível)
- **Imagens secundárias**: `quality={75}` (qualidade otimizada)
- **Ícones pequenos**: `quality={75}` (suficiente para ícones)

#### 3. **Configuração Next.js**
```typescript
// next.config.ts
images: {
  formats: ['image/webp', 'image/avif'], // Formatos modernos
  minimumCacheTTL: 60, // Cache otimizado
  dangerouslyAllowSVG: true, // Suporte a SVG
  contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
}
```

## 🚀 Benefícios das Otimizações

### Performance Metrics
- **LCP (Largest Contentful Paint)**: Melhorado com priority estratégico
- **CLS (Cumulative Layout Shift)**: Reduzido com dimensões fixas
- **FCP (First Contentful Paint)**: Otimizado com formatos modernos

### Tamanho de Arquivos
- **WebP/AVIF**: Redução de 25-35% no tamanho vs PNG/JPEG
- **Quality 75**: Redução de ~40% vs quality 100 com qualidade visual similar
- **Quality 90**: Redução de ~20% vs quality 100 mantendo alta qualidade

## 🔧 Configurações Técnicas

### Formatos de Imagem Suportados
1. **AVIF** (primeira prioridade): Melhor compressão
2. **WebP** (fallback): Boa compressão, amplo suporte
3. **PNG/JPEG** (fallback final): Compatibilidade total

### Cache Strategy
- **TTL mínimo**: 60 segundos
- **Browser cache**: Otimizado pelo Next.js
- **CDN ready**: Preparado para deploy em Vercel

## 📱 Otimizações Responsivas

### Dimensões Adaptáveis
```typescript
// Imagem que se adapta ao tamanho da tela
<Image
  src="/cereja.png"
  alt="Cereja"
  width={80}
  height={80}
  className="object-contain sm:w-[100px] sm:h-[100px]"
  quality={90}
/>
```

### Breakpoints Otimizados
- **Mobile**: 80x80px (menor para economizar dados)
- **Desktop**: 100x100px (maior para melhor qualidade)

## 🛡️ Segurança

### Content Security Policy
```
default-src 'self'; script-src 'none'; sandbox;
```
- Previne execução de scripts maliciosos em SVGs
- Sandbox isola conteúdo SVG
- Permite apenas recursos do próprio domínio

## 📊 Monitoramento

### Core Web Vitals
- **LCP**: < 2.5s (Good)
- **FID**: < 100ms (Good)  
- **CLS**: < 0.1 (Good)

### Ferramentas de Medição
- **Lighthouse**: Auditoria automática
- **PageSpeed Insights**: Métricas reais
- **Next.js Analytics**: Monitoramento contínuo

## 🔄 Melhores Práticas Implementadas

### 1. **Priority Loading**
- Apenas imagens above-the-fold têm `priority={true}`
- Imagens da sidebar carregam sob demanda
- Ícones pequenos não têm prioridade

### 2. **Quality Strategy**
- **90%**: Imagens principais visíveis
- **75%**: Imagens secundárias e ícones
- **Automático**: Next.js otimiza baseado no dispositivo

### 3. **Format Selection**
- AVIF para navegadores modernos (Chrome 85+, Firefox 93+)
- WebP para navegadores intermediários (Chrome 23+, Firefox 65+)
- PNG/JPEG para navegadores legados

### 4. **Lazy Loading**
- Automático para imagens sem `priority`
- Intersection Observer nativo
- Placeholder blur automático

## 🐛 Troubleshooting

### Warning: "Resource preloaded but not used"
**Causa**: Imagem com `priority={true}` não visível imediatamente
**Solução**: Remover `priority` de imagens não críticas

### Imagens não carregando
**Causa**: CSP muito restritivo ou formato não suportado
**Solução**: Verificar configuração de `images` no next.config.ts

### Performance degradada
**Causa**: Muitas imagens com `priority={true}`
**Solução**: Usar `priority` apenas para 1-2 imagens críticas

## 📈 Resultados Esperados

### Antes das Otimizações
- ⚠️ Warnings de preload desnecessário
- 📊 Quality 100% em todas as imagens
- 🐌 Carregamento mais lento em conexões lentas

### Depois das Otimizações
- ✅ Sem warnings de preload
- 📊 Quality otimizada por uso
- 🚀 Carregamento 25-40% mais rápido
- 💾 Menor uso de dados móveis

## 🔮 Próximas Otimizações

1. **Blur Placeholder**: Adicionar placeholders para melhor UX
2. **Responsive Images**: Diferentes tamanhos para diferentes telas
3. **Image Sprites**: Combinar ícones pequenos em sprite sheets
4. **Progressive Loading**: Carregamento progressivo para imagens grandes

As otimizações implementadas seguem as melhores práticas do Next.js 15 e garantem excelente performance em todos os dispositivos.
