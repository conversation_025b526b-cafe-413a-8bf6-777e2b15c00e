'use client';

import { useState, useEffect } from 'react';
import { X, Shield, BarChart3, Target, Info } from 'lucide-react';
import { 
  getSavedPreferences, 
  updateConsentMode, 
  DEFAULT_PREFERENCES,
  type UserPreferences 
} from '@/lib/consent';

interface CookieSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface CookieCategory {
  id: keyof UserPreferences;
  name: string;
  description: string;
  icon: React.ReactNode;
  required: boolean;
  details: string[];
}

const cookieCategories: CookieCategory[] = [
  {
    id: 'essential',
    name: '<PERSON><PERSON> Essenciais',
    description: 'Necessários para o funcionamento básico do site',
    icon: <Shield className="w-5 h-5" />,
    required: true,
    details: [
      'Preferências de consentimento de cookies',
      'Configurações da calculadora (quantidade de café, perfil de sabor)',
      'Estado do cronômetro e receita ativa',
      'Funcionalidades básicas de navegação e interface'
    ]
  },
  {
    id: 'analytics',
    name: 'Cookies de Análise',
    description: 'Nos ajudam a entender como você usa o site',
    icon: <BarChart3 className="w-5 h-5" />,
    required: false,
    details: [
      'Google Analytics para métricas de uso',
      'Análise de performance das páginas',
      'Identificação de problemas técnicos',
      'Melhoria da experiência do usuário'
    ]
  },
  {
    id: 'marketing',
    name: 'Cookies de Marketing',
    description: 'Utilizados para personalização e publicidade',
    icon: <Target className="w-5 h-5" />,
    required: false,
    details: [
      'Google AdSense para anúncios relevantes',
      'Personalização de conteúdo',
      'Remarketing e retargeting',
      'Análise de campanhas publicitárias'
    ]
  }
];

export function CookieSettingsModal({ isOpen, onClose }: CookieSettingsModalProps) {
  const [preferences, setPreferences] = useState<UserPreferences>(DEFAULT_PREFERENCES);
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      const savedPreferences = getSavedPreferences();
      if (savedPreferences) {
        setPreferences(savedPreferences);
      }
    }
  }, [isOpen]);

  const handleToggle = (categoryId: keyof UserPreferences) => {
    if (categoryId === 'essential') return; // Não pode ser desabilitado

    setPreferences(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const handleSave = () => {
    updateConsentMode(preferences);
    onClose();
  };

  const handleAcceptAll = () => {
    const allAccepted: UserPreferences = {
      essential: true,
      analytics: true,
      marketing: true,
      timestamp: Date.now(),
      version: '1.0'
    };
    setPreferences(allAccepted);
    updateConsentMode(allAccepted);
    onClose();
  };

  const handleRejectAll = () => {
    const allRejected: UserPreferences = {
      essential: true,
      analytics: false,
      marketing: false,
      timestamp: Date.now(),
      version: '1.0'
    };
    setPreferences(allRejected);
    updateConsentMode(allRejected);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">
              Configurações de Cookies
            </h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            <p className="text-gray-600 mb-6">
              Gerencie suas preferências de cookies. Você pode ativar ou desativar diferentes 
              categorias de cookies de acordo com suas preferências.
            </p>

            <div className="space-y-4">
              {cookieCategories.map((category) => (
                <div key={category.id} className="border border-gray-200 rounded-lg">
                  <div className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-purple-600">
                          {category.icon}
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">
                            {category.name}
                          </h3>
                          <p className="text-sm text-gray-600">
                            {category.description}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setExpandedCategory(
                            expandedCategory === category.id ? null : category.id
                          )}
                          className="p-1 text-gray-400 hover:text-gray-600"
                        >
                          <Info className="w-4 h-4" />
                        </button>
                        
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={preferences[category.id] as boolean}
                            onChange={() => handleToggle(category.id)}
                            disabled={category.required}
                            className="sr-only peer"
                          />
                          <div className={`
                            relative w-11 h-6 rounded-full transition-colors
                            ${category.required 
                              ? 'bg-gray-300 cursor-not-allowed' 
                              : 'bg-gray-200 peer-checked:bg-purple-600 peer-focus:ring-4 peer-focus:ring-purple-300'
                            }
                          `}>
                            <div className={`
                              absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-5 w-5 transition-transform
                              ${preferences[category.id] ? 'translate-x-full' : 'translate-x-0'}
                            `} />
                          </div>
                        </label>
                      </div>
                    </div>

                    {expandedCategory === category.id && (
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <h4 className="font-medium text-gray-900 mb-2">
                          O que inclui:
                        </h4>
                        <ul className="space-y-1">
                          {category.details.map((detail, index) => (
                            <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                              <span className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                              {detail}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="flex flex-col sm:flex-row gap-3 p-6 border-t border-gray-200">
            <button
              onClick={handleRejectAll}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              Rejeitar Todos
            </button>
            
            <button
              onClick={handleAcceptAll}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              Aceitar Todos
            </button>
            
            <button
              onClick={handleSave}
              className="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors sm:ml-auto"
            >
              Salvar Preferências
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
