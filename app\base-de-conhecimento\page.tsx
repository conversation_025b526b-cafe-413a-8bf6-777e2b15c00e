"use client";

import Image from "next/image";
import Link from "next/link";
import { BookOpen as BookOpenIcon, Home, BookOpen } from "lucide-react";

export default function BaseDeConhecimento() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Conteúdo principal */}
      <div className="flex-1 flex flex-col items-center justify-center px-4 sm:px-8 py-8">
        {/* Logo e título */}
        <div className="flex items-center gap-3 mb-8">
          <Image
            src="/cereja.png"
            alt="Cereja"
            width={60}
            height={60}
            className="object-contain"
            quality={75}
          />
          <div className="text-center">
            <h1 className="text-3xl sm:text-4xl font-bold text-foreground">
              cereja
            </h1>
            <p className="text-xs sm:text-sm text-muted-foreground mt-1">
              calculadora do método 4:6
            </p>
          </div>
        </div>

        {/* Card de Em Construção */}
        <div className="bg-card p-8 sm:p-12 rounded-lg border border-border shadow-md max-w-md w-full text-center">
          {/* Ícone de livro */}
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-primary/10 rounded-full">
              <BookOpenIcon size={48} className="text-primary" />
            </div>
          </div>

          {/* Título */}
          <h2 className="text-2xl sm:text-3xl font-bold text-foreground mb-4">
            Página em Desenvolvimento
          </h2>

          {/* Mensagem */}
          <p className="text-muted-foreground mb-8 leading-relaxed">
            A Base de Conhecimento está sendo criada e estará disponível em breve.
            Enquanto isso, você pode usar nossa calculadora e explorar o glossário de termos do café.
          </p>

          {/* Botões de navegação */}
          <div className="space-y-3">
            <Link
              href="/"
              className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-primary text-primary-foreground rounded-md font-medium hover:bg-primary/90 transition-colors"
            >
              <Home size={20} />
              Voltar ao Início
            </Link>

            <Link
              href="/glossario"
              className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-secondary text-secondary-foreground rounded-md font-medium hover:bg-accent hover:text-accent-foreground transition-colors"
            >
              <BookOpen size={20} />
              Ir para Glossário
            </Link>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-secondary/50 py-4 w-full text-center">
        <p className="text-xs text-muted-foreground">
          Desenvolvido por{" "}
          <a
            href="https://ko-fi.com/pedrogott"
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:text-primary/80 transition-colors font-medium"
          >
            Pedro Gottardi
          </a>
        </p>
      </footer>
    </div>
  );
}